#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@turbo+gen@1.13.4_@types+node@22.15.29_typescript@5.8.3/node_modules/@turbo/gen/dist/node_modules:/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@turbo+gen@1.13.4_@types+node@22.15.29_typescript@5.8.3/node_modules/@turbo/gen/node_modules:/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@turbo+gen@1.13.4_@types+node@22.15.29_typescript@5.8.3/node_modules/@turbo/node_modules:/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@turbo+gen@1.13.4_@types+node@22.15.29_typescript@5.8.3/node_modules:/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@turbo+gen@1.13.4_@types+node@22.15.29_typescript@5.8.3/node_modules/@turbo/gen/dist/node_modules:/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@turbo+gen@1.13.4_@types+node@22.15.29_typescript@5.8.3/node_modules/@turbo/gen/node_modules:/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@turbo+gen@1.13.4_@types+node@22.15.29_typescript@5.8.3/node_modules/@turbo/node_modules:/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@turbo+gen@1.13.4_@types+node@22.15.29_typescript@5.8.3/node_modules:/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@turbo/gen/dist/cli.js" "$@"
else
  exec node  "$basedir/../@turbo/gen/dist/cli.js" "$@"
fi
