# @emotion/is-prop-valid

## 0.8.8

### Patch Changes

- [`babbbe3`](https://github.com/emotion-js/emotion/commit/babbbe36844f26f6d7041f1d3aeb47d5dfb08d8a) [#1792](https://github.com/emotion-js/emotion/pull/1792) Thanks [@egdbear](https://github.com/egdbear)! - Adds `disablePictureInPicture` to the list of allowed props.

## 0.8.7

### Patch Changes

- [`12141c5`](https://github.com/emotion-js/emotion/commit/12141c54318c0738b60bf755e033cf6e12238a02) [#1736](https://github.com/emotion-js/emotion/pull/1736) Thanks [@bezoerb](https://github.com/bezoerb)! - Adds inert to the list of allowed props

## 0.8.6

### Patch Changes

- [`4c62ae9`](https://github.com/emotion-js/emotion/commit/4c62ae9447959d438928e1a26f76f1487983c968) [#1698](https://github.com/emotion-js/emotion/pull/1698) Thanks [@Andarist](https://github.com/Andarist)! - Add LICENSE file
- Updated dependencies [[`4c62ae9`](https://github.com/emotion-js/emotion/commit/4c62ae9447959d438928e1a26f76f1487983c968)]:
  - @emotion/memoize@0.7.4

## 0.8.5

### Patch Changes

- [`5e17e456`](https://github.com/emotion-js/emotion/commit/5e17e456a66857bb3a3a5b39c9cd8f8dd89301e5) [#1596](https://github.com/emotion-js/emotion/pull/1596) Thanks [@Andarist](https://github.com/Andarist)! - Added Flow types to the package.

## 0.8.4

### Patch Changes

- [`6cdb5695`](https://github.com/emotion-js/emotion/commit/6cdb56959bc4b64d7178604f1eb64a058c2e58c2) [#1584](https://github.com/emotion-js/emotion/pull/1584) Thanks [@probablyup](https://github.com/probablyup)! - add "on" amp html attribute to the whitelist

## 0.8.3

- Updated dependencies [c81c0033]:
  - @emotion/memoize@0.7.3

## 0.8.2

### Patch Changes

- [c0eb604d](https://github.com/emotion-js/emotion/commit/c0eb604d) [#1419](https://github.com/emotion-js/emotion/pull/1419) Thanks [@mitchellhamilton](https://github.com/mitchellhamilton)! - Update build tool

## 0.8.1

### Patch Changes

- [52bd655b](https://github.com/emotion-js/emotion/commit/52bd655b) [#1379](https://github.com/emotion-js/emotion/pull/1379) Thanks [@Andarist](https://github.com/Andarist)! - Add decoding as valid prop

## 0.8.0

### Minor Changes

- [06426c95](https://github.com/emotion-js/emotion/commit/06426c95) [#1377](https://github.com/emotion-js/emotion/pull/1377) Thanks [@AjayPoshak](https://github.com/AjayPoshak)! - Mark loading as a valid property. This property is used to lazily load images and iFrames.
