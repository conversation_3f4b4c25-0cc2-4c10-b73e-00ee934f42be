'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/lib/stores/auth-store';
import { clientsAPI } from '@/lib/api';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { toast } from 'sonner';
import { Users, Plus, Search, Phone, Mail, MapPin, Building, Edit, Trash2 } from 'lucide-react';
import { UnifiedLayout } from '@/components/layout';
import { RoleGuard } from '@/components/auth/role-guard';
import { UserRole } from '@mtbrmg/shared';

export default function ClientsPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingClient, setEditingClient] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    website: '',
    address: '',
    governorate: '',
    mood: 'neutral',
    notes: ''
  });

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, router, mounted]);

  useEffect(() => {
    if (mounted && isAuthenticated) {
      fetchClients();
    }
  }, [mounted, isAuthenticated]);

  const fetchClients = async () => {
    try {
      const data = await clientsAPI.getClients();
      setClients(data.results || []);
    } catch (error) {
      console.error('Error fetching clients:', error);
      toast.error('فشل في تحميل العملاء');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateClient = async () => {
    try {
      const newClient = await clientsAPI.createClient(formData);
      setClients([newClient, ...clients]);
      setIsCreateDialogOpen(false);
      resetForm();
      toast.success('تم إضافة العميل بنجاح');
    } catch (error) {
      console.error('Error creating client:', error);
      toast.error('فشل في إضافة العميل');
    }
  };

  const handleEditClient = async () => {
    try {
      const updatedClient = await clientsAPI.updateClient(editingClient.id, formData);
      setClients(clients.map(client =>
        client.id === editingClient.id ? updatedClient : client
      ));
      setIsEditDialogOpen(false);
      setEditingClient(null);
      resetForm();
      toast.success('تم تحديث العميل بنجاح');
    } catch (error) {
      console.error('Error updating client:', error);
      toast.error('فشل في تحديث العميل');
    }
  };

  const handleDeleteClient = async (clientId: number) => {
    try {
      await clientsAPI.deleteClient(clientId.toString());
      setClients(clients.filter(client => client.id !== clientId));
      toast.success('تم حذف العميل بنجاح');
    } catch (error) {
      console.error('Error deleting client:', error);
      toast.error('فشل في حذف العميل');
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      email: '',
      phone: '',
      company: '',
      website: '',
      address: '',
      governorate: '',
      mood: 'neutral',
      notes: ''
    });
  };

  const openEditDialog = (client: any) => {
    setEditingClient(client);
    setFormData({
      name: client.name || '',
      email: client.email || '',
      phone: client.phone || '',
      company: client.company || '',
      website: client.website || '',
      address: client.address || '',
      governorate: client.governorate || '',
      mood: client.mood || 'neutral',
      notes: client.notes || ''
    });
    setIsEditDialogOpen(true);
  };

  const getMoodColor = (mood: string) => {
    switch (mood) {
      case 'happy': return 'bg-green-100 text-green-800';
      case 'concerned': return 'bg-yellow-100 text-yellow-800';
      case 'angry': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getMoodText = (mood: string) => {
    switch (mood) {
      case 'happy': return 'راضي';
      case 'concerned': return 'قلق';
      case 'angry': return 'غاضب';
      default: return 'غير محدد';
    }
  };

  const filteredClients = clients.filter((client: any) =>
    client.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.company?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (!mounted || !isAuthenticated || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-brand-primary">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري التحميل...</p>
        </div>
      </div>
    );
  }

  return (
    <RoleGuard allowedRoles={[UserRole.ADMIN, UserRole.SALES_MANAGER, UserRole.MEDIA_BUYER]}>
      <UnifiedLayout>
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">إدارة العملاء</h1>
              <p className="text-gray-600 mt-1">إدارة قاعدة بيانات العملاء والتواصل معهم</p>
            </div>
            <div className="flex gap-4">
              <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                <DialogTrigger asChild>
                  <Button onClick={() => resetForm()}>
                    <Plus className="ml-2 h-4 w-4" />
                    إضافة عميل جديد
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[600px]">
                  <DialogHeader>
                    <DialogTitle>إضافة عميل جديد</DialogTitle>
                    <DialogDescription>
                      أدخل بيانات العميل الجديد
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">اسم العميل *</Label>
                        <Input
                          id="name"
                          value={formData.name}
                          onChange={(e) => setFormData({...formData, name: e.target.value})}
                          placeholder="اسم العميل"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">البريد الإلكتروني *</Label>
                        <Input
                          id="email"
                          type="email"
                          value={formData.email}
                          onChange={(e) => setFormData({...formData, email: e.target.value})}
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="phone">رقم الهاتف</Label>
                        <Input
                          id="phone"
                          value={formData.phone}
                          onChange={(e) => setFormData({...formData, phone: e.target.value})}
                          placeholder="+201234567890"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="company">اسم الشركة</Label>
                        <Input
                          id="company"
                          value={formData.company}
                          onChange={(e) => setFormData({...formData, company: e.target.value})}
                          placeholder="اسم الشركة"
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="website">الموقع الإلكتروني</Label>
                        <Input
                          id="website"
                          value={formData.website}
                          onChange={(e) => setFormData({...formData, website: e.target.value})}
                          placeholder="https://example.com"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="governorate">المحافظة</Label>
                        <Select value={formData.governorate} onValueChange={(value) => setFormData({...formData, governorate: value})}>
                          <SelectTrigger>
                            <SelectValue placeholder="اختر المحافظة" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="cairo">القاهرة</SelectItem>
                            <SelectItem value="giza">الجيزة</SelectItem>
                            <SelectItem value="alexandria">الإسكندرية</SelectItem>
                            <SelectItem value="qalyubia">القليوبية</SelectItem>
                            <SelectItem value="port_said">بورسعيد</SelectItem>
                            <SelectItem value="suez">السويس</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="mood">حالة العميل</Label>
                        <Select value={formData.mood} onValueChange={(value) => setFormData({...formData, mood: value})}>
                          <SelectTrigger>
                            <SelectValue placeholder="اختر حالة العميل" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="happy">راضي 😊</SelectItem>
                            <SelectItem value="neutral">محايد 😐</SelectItem>
                            <SelectItem value="concerned">قلق 😟</SelectItem>
                            <SelectItem value="angry">غاضب 😠</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="address">العنوان</Label>
                        <Input
                          id="address"
                          value={formData.address}
                          onChange={(e) => setFormData({...formData, address: e.target.value})}
                          placeholder="العنوان التفصيلي"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="notes">ملاحظات</Label>
                      <Textarea
                        id="notes"
                        value={formData.notes}
                        onChange={(e) => setFormData({...formData, notes: e.target.value})}
                        placeholder="ملاحظات إضافية عن العميل"
                        rows={3}
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                      إلغاء
                    </Button>
                    <Button onClick={handleCreateClient} disabled={!formData.name || !formData.email}>
                      إضافة العميل
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              {/* Edit Dialog */}
              <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                <DialogContent className="sm:max-w-[600px]">
                  <DialogHeader>
                    <DialogTitle>تعديل بيانات العميل</DialogTitle>
                    <DialogDescription>
                      تعديل بيانات العميل {editingClient?.name}
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="edit-name">اسم العميل *</Label>
                        <Input
                          id="edit-name"
                          value={formData.name}
                          onChange={(e) => setFormData({...formData, name: e.target.value})}
                          placeholder="اسم العميل"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="edit-email">البريد الإلكتروني *</Label>
                        <Input
                          id="edit-email"
                          type="email"
                          value={formData.email}
                          onChange={(e) => setFormData({...formData, email: e.target.value})}
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="edit-phone">رقم الهاتف</Label>
                        <Input
                          id="edit-phone"
                          value={formData.phone}
                          onChange={(e) => setFormData({...formData, phone: e.target.value})}
                          placeholder="+201234567890"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="edit-company">اسم الشركة</Label>
                        <Input
                          id="edit-company"
                          value={formData.company}
                          onChange={(e) => setFormData({...formData, company: e.target.value})}
                          placeholder="اسم الشركة"
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="edit-website">الموقع الإلكتروني</Label>
                        <Input
                          id="edit-website"
                          value={formData.website}
                          onChange={(e) => setFormData({...formData, website: e.target.value})}
                          placeholder="https://example.com"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="edit-governorate">المحافظة</Label>
                        <Select value={formData.governorate} onValueChange={(value) => setFormData({...formData, governorate: value})}>
                          <SelectTrigger>
                            <SelectValue placeholder="اختر المحافظة" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="cairo">القاهرة</SelectItem>
                            <SelectItem value="giza">الجيزة</SelectItem>
                            <SelectItem value="alexandria">الإسكندرية</SelectItem>
                            <SelectItem value="qalyubia">القليوبية</SelectItem>
                            <SelectItem value="port_said">بورسعيد</SelectItem>
                            <SelectItem value="suez">السويس</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="edit-mood">حالة العميل</Label>
                        <Select value={formData.mood} onValueChange={(value) => setFormData({...formData, mood: value})}>
                          <SelectTrigger>
                            <SelectValue placeholder="اختر حالة العميل" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="happy">راضي 😊</SelectItem>
                            <SelectItem value="neutral">محايد 😐</SelectItem>
                            <SelectItem value="concerned">قلق 😟</SelectItem>
                            <SelectItem value="angry">غاضب 😠</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="edit-address">العنوان</Label>
                        <Input
                          id="edit-address"
                          value={formData.address}
                          onChange={(e) => setFormData({...formData, address: e.target.value})}
                          placeholder="العنوان التفصيلي"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="edit-notes">ملاحظات</Label>
                      <Textarea
                        id="edit-notes"
                        value={formData.notes}
                        onChange={(e) => setFormData({...formData, notes: e.target.value})}
                        placeholder="ملاحظات إضافية عن العميل"
                        rows={3}
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                      إلغاء
                    </Button>
                    <Button onClick={handleEditClient} disabled={!formData.name || !formData.email}>
                      حفظ التغييرات
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex gap-4">
              <div className="relative flex-1">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في العملاء..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي العملاء</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{clients.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">العملاء الراضون</CardTitle>
              <Users className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {clients.filter((c: any) => c.mood === 'happy').length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">العملاء القلقون</CardTitle>
              <Users className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                {clients.filter((c: any) => c.mood === 'concerned').length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">العملاء الغاضبون</CardTitle>
              <Users className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {clients.filter((c: any) => c.mood === 'angry').length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Clients List */}
        {loading ? (
          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-gray-600">جاري تحميل العملاء...</p>
              </div>
            </CardContent>
          </Card>
        ) : filteredClients.length === 0 ? (
          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <Users className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">لا توجد عملاء</h3>
                <p className="mt-1 text-sm text-gray-500">ابدأ بإضافة عميل جديد</p>
                <div className="mt-6">
                  <Button>
                    <Plus className="ml-2 h-4 w-4" />
                    إضافة عميل جديد
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredClients.map((client: any) => (
              <Card key={client.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">{client.name}</CardTitle>
                      {client.company && (
                        <CardDescription className="flex items-center mt-1">
                          <Building className="h-4 w-4 ml-1" />
                          {client.company}
                        </CardDescription>
                      )}
                    </div>
                    <Badge className={getMoodColor(client.mood)}>
                      {getMoodText(client.mood)}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {client.email && (
                      <div className="flex items-center text-sm text-gray-600">
                        <Mail className="h-4 w-4 ml-2" />
                        {client.email}
                      </div>
                    )}
                    {client.phone && (
                      <div className="flex items-center text-sm text-gray-600">
                        <Phone className="h-4 w-4 ml-2" />
                        {client.phone}
                      </div>
                    )}
                    {client.governorate && (
                      <div className="flex items-center text-sm text-gray-600">
                        <MapPin className="h-4 w-4 ml-2" />
                        {client.governorate}
                      </div>
                    )}
                  </div>
                  <div className="mt-4 flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openEditDialog(client)}
                      className="flex-1"
                    >
                      <Edit className="h-4 w-4 ml-1" />
                      تعديل
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>تأكيد الحذف</AlertDialogTitle>
                          <AlertDialogDescription>
                            هل أنت متأكد من حذف العميل "{client.name}"؟ لا يمكن التراجع عن هذا الإجراء.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>إلغاء</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDeleteClient(client.id)}
                            className="bg-red-600 hover:bg-red-700"
                          >
                            حذف
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
        </div>
      </UnifiedLayout>
    </RoleGuard>
  );
}
